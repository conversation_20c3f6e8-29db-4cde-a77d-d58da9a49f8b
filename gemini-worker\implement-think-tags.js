#!/usr/bin/env node

/**
 * 实现标准 <think> 标签格式的思考内容处理
 * 基于 DeepSeek R1、KoboldCpp 等项目的标准实现
 */

const fs = require('fs');
const path = require('path');

// 标准思考内容格式处理函数
const thinkTagImplementation = `
// 标准 <think> 标签处理 - 基于 DeepSeek R1 和 KoboldCpp 标准
function processThinkTags(content, showThinking = true, thoughtToContent = false) {
  if (!content) return { thinking: '', answer: content };
  
  // 检测标准 <think> 标签格式
  const thinkTagPattern = /<think>([\\s\\S]*?)<\\/think>/g;
  let thinkingContent = '';
  let processedContent = content;
  
  // 提取所有思考内容
  let match;
  while ((match = thinkTagPattern.exec(content)) !== null) {
    thinkingContent += match[1].trim() + '\\n\\n';
  }
  
  // 移除思考标签，保留答案内容
  processedContent = content.replace(thinkTagPattern, '').trim();
  
  // 如果没有找到标准标签，尝试检测 Gemini 格式
  if (!thinkingContent) {
    const geminiResult = parseGeminiThinkingContent(content);
    if (geminiResult.thinking) {
      thinkingContent = geminiResult.thinking;
      processedContent = geminiResult.answer;
    }
  }
  
  return {
    thinking: thinkingContent.trim(),
    answer: processedContent.trim(),
    hasThinking: !!thinkingContent.trim()
  };
}

// 将思考内容包装为标准 <think> 标签格式
function wrapInThinkTags(thinkingContent) {
  if (!thinkingContent) return '';
  return \`<think>\\n\${thinkingContent}\\n</think>\\n\\n\`;
}

// 格式化思考内容用于显示
function formatThinkingForDisplay(thinkingContent, format = 'standard') {
  if (!thinkingContent) return '';
  
  switch (format) {
    case 'standard':
      // 标准 <think> 标签格式 - 被大多数客户端支持
      return wrapInThinkTags(thinkingContent);
      
    case 'markdown':
      // Markdown 折叠格式 - 适用于支持 Markdown 的客户端
      return \`<details>\\n<summary>💭 思考过程</summary>\\n\\n\${thinkingContent}\\n\\n</details>\\n\\n\`;
      
    case 'simple':
      // 简单标记格式 - 兼容性最好
      return \`**💭 思考：**\\n\\n\${thinkingContent}\\n\\n---\\n\\n\`;
      
    case 'reasoning':
      // 使用 reasoning 字段（仅限支持的客户端）
      return null; // 这种情况下应该使用 delta.reasoning
      
    default:
      return wrapInThinkTags(thinkingContent);
  }
}

// 检测客户端类型并选择最佳格式
function detectClientAndFormat(userAgent, acceptHeader) {
  // 基于 User-Agent 和 Accept 头检测客户端类型
  const ua = (userAgent || '').toLowerCase();
  const accept = (acceptHeader || '').toLowerCase();
  
  // Cherry Studio 和类似客户端通常支持标准格式
  if (ua.includes('cherry') || ua.includes('sillytavern') || ua.includes('agnaistic')) {
    return 'standard';
  }
  
  // LobeChat 和现代客户端支持 Markdown
  if (ua.includes('lobe') || ua.includes('chatgpt-next-web') || accept.includes('text/markdown')) {
    return 'markdown';
  }
  
  // 默认使用标准格式
  return 'standard';
}
`;

// 更新 worker.js 文件以实现 <think> 标签支持
function updateWorkerWithThinkTags() {
    const workerPath = path.join(__dirname, 'worker - D1.js');
    
    if (!fs.existsSync(workerPath)) {
        console.error('❌ worker - D1.js 文件不存在');
        return false;
    }
    
    let workerContent = fs.readFileSync(workerPath, 'utf8');
    
    // 1. 添加新的思考内容处理函数
    const insertPosition = workerContent.indexOf('// 检测和分离 Gemini 2.5 的思考内容');
    if (insertPosition === -1) {
        console.error('❌ 找不到插入位置');
        return false;
    }
    
    // 插入新的函数
    const newFunctions = `
${thinkTagImplementation}

`;
    
    workerContent = workerContent.slice(0, insertPosition) + newFunctions + workerContent.slice(insertPosition);
    
    // 2. 更新非流式响应处理
    workerContent = workerContent.replace(
        /\/\/ 处理思考内容[\s\S]*?console\.log\('处理非流式思考内容:'.*?\);/,
        `// 处理思考内容
      if (thinkingContent) {
        // 预先获取配置
        const showThinking = await getConfigValue(env, 'show_thinking_process', CONFIG.SHOW_THINKING_PROCESS);
        const thoughtToContent = await getConfigValue(env, 'thought_to_content_enabled', CONFIG.THOUGHT_TO_CONTENT_ENABLED);
        
        if (showThinking === 'true' || showThinking === true) {
          // 检测客户端类型
          const userAgent = request.headers.get('user-agent');
          const acceptHeader = request.headers.get('accept');
          const clientFormat = detectClientAndFormat(userAgent, acceptHeader);
          
          if (thoughtToContent === 'true' || thoughtToContent === true) {
            // 使用标准 <think> 标签格式
            const formattedThought = formatThinkingForDisplay(thinkingContent, clientFormat);
            if (formattedThought) {
              message.content = formattedThought + (message.content || '');
              hasText = true;
            }
          }
          // 注意：非流式响应中，reasoning 字段通常不被标准 OpenAI 客户端支持
          // 所以我们主要使用 content 字段来传递思考内容
        }
        console.log('处理非流式思考内容:', thinkingContent.substring(0, 100));
      }`
    );
    
    // 3. 更新假流式响应处理
    workerContent = workerContent.replace(
        /\/\/ 发送思考内容[\s\S]*?console\.log\('发送假流式思考内容:'.*?\);/,
        `// 发送思考内容
                if (parsed.thinking && (showThinking === 'true' || showThinking === true)) {
                  thinkingContent += parsed.thinking;
                  
                  // 检测客户端类型
                  const userAgent = originalBody.headers?.['user-agent'] || '';
                  const acceptHeader = originalBody.headers?.['accept'] || '';
                  const clientFormat = detectClientAndFormat(userAgent, acceptHeader);
                  
                  if (thoughtToContent === 'true' || thoughtToContent === true) {
                    // 使用标准格式发送思考内容
                    const formattedThought = formatThinkingForDisplay(parsed.thinking, clientFormat);
                    if (formattedThought) {
                      const thoughtChunk = {
                        id: \`chatcmpl-\${Date.now()}\`,
                        object: "chat.completion.chunk",
                        created: Math.floor(Date.now() / 1000),
                        model: originalBody.model || "gemini-2.0-flash",
                        choices: [{
                          index: 0,
                          delta: { content: formattedThought },
                          finish_reason: null
                        }]
                      };
                      controller.enqueue(new TextEncoder().encode(\`data: \${JSON.stringify(thoughtChunk)}\\n\\n\`));
                    }
                  } else {
                    // 作为特殊的思考块发送（使用标准 <think> 标签）
                    const wrappedThinking = wrapInThinkTags(parsed.thinking);
                    const thoughtChunk = {
                      id: \`chatcmpl-\${Date.now()}\`,
                      object: "chat.completion.chunk",
                      created: Math.floor(Date.now() / 1000),
                      model: originalBody.model || "gemini-2.0-flash",
                      choices: [{
                        index: 0,
                        delta: { content: wrappedThinking },
                        finish_reason: null
                      }]
                    };
                    controller.enqueue(new TextEncoder().encode(\`data: \${JSON.stringify(thoughtChunk)}\\n\\n\`));
                  }
                  console.log('发送假流式思考内容:', parsed.thinking.substring(0, 100));
                }`
    );
    
    // 4. 更新真实流式响应处理
    workerContent = workerContent.replace(
        /if \(showThinking === 'true' \|\| showThinking === true\) \{[\s\S]*?console\.log\('处理思考内容:'.*?\);[\s\S]*?\}/,
        `if (showThinking === 'true' || showThinking === true) {
                        // 检测客户端类型
                        const userAgent = originalBody.headers?.['user-agent'] || '';
                        const acceptHeader = originalBody.headers?.['accept'] || '';
                        const clientFormat = detectClientAndFormat(userAgent, acceptHeader);
                        
                        if (thoughtToContent === 'true' || thoughtToContent === true) {
                          // 使用标准格式发送思考内容
                          const formattedThought = formatThinkingForDisplay(part.thought, clientFormat);
                          if (formattedThought) {
                            const thoughtChunk = {
                              id: \`chatcmpl-\${Date.now()}\`,
                              object: "chat.completion.chunk",
                              created: Math.floor(Date.now() / 1000),
                              model: originalBody.model || "gemini-2.0-flash",
                              choices: [{
                                index: 0,
                                delta: { content: formattedThought },
                                finish_reason: null
                              }]
                            };
                            controller.enqueue(new TextEncoder().encode(\`data: \${JSON.stringify(thoughtChunk)}\\n\\n\`));
                          }
                        } else {
                          // 作为标准 <think> 标签发送
                          const wrappedThinking = wrapInThinkTags(part.thought);
                          const thoughtChunk = {
                            id: \`chatcmpl-\${Date.now()}\`,
                            object: "chat.completion.chunk",
                            created: Math.floor(Date.now() / 1000),
                            model: originalBody.model || "gemini-2.0-flash",
                            choices: [{
                              index: 0,
                              delta: { content: wrappedThinking },
                              finish_reason: null
                            }]
                          };
                          controller.enqueue(new TextEncoder().encode(\`data: \${JSON.stringify(thoughtChunk)}\\n\\n\`));
                        }
                      }
                      console.log('处理思考内容:', part.thought.substring(0, 100));`
    );
    
    // 写入更新后的文件
    fs.writeFileSync(workerPath, workerContent, 'utf8');
    console.log('✅ worker - D1.js 已更新，添加了标准 <think> 标签支持');
    
    return true;
}

// 创建性能优化脚本
function createPerformanceOptimizer() {
    const optimizerContent = `#!/usr/bin/env node

/**
 * 性能优化脚本 - 解决响应时间过长问题
 */

const https = require('https');

async function optimizePerformance() {
    const apiUrl = 'https://g.551543.xyz';
    const apiKey = 'sk-mariahlamb';
    
    console.log('🚀 开始性能优化');
    console.log('=' .repeat(50));
    
    // 1. 禁用假流式模式以提高响应速度
    console.log('\\n⚡ 禁用假流式模式...');
    try {
        const response = await fetch(\`\${apiUrl}/admin/api/update-fake-stream-config\`, {
            method: 'POST',
            headers: {
                'Authorization': \`Bearer \${apiKey}\`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                enabled: false  // 禁用假流式，使用真实流式
            })
        });
        
        const result = await response.json();
        if (result.success) {
            console.log('✅ 假流式模式已禁用，现在使用真实流式响应');
        } else {
            console.log('❌ 禁用假流式失败:', result.message);
        }
        
    } catch (error) {
        console.error('禁用假流式失败:', error);
    }
    
    // 2. 优化思考功能配置
    console.log('\\n🧠 优化思考功能配置...');
    try {
        const response = await fetch(\`\${apiUrl}/admin/api/update-thinking-config\`, {
            method: 'POST',
            headers: {
                'Authorization': \`Bearer \${apiKey}\`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                showThinking: true,
                thoughtToContent: true  // 启用标准 <think> 标签格式
            })
        });
        
        const result = await response.json();
        if (result.success) {
            console.log('✅ 思考功能已优化，启用标准 <think> 标签格式');
        } else {
            console.log('❌ 优化思考功能失败:', result.message);
        }
        
    } catch (error) {
        console.error('优化思考功能失败:', error);
    }
    
    console.log('\\n🎉 性能优化完成！');
    console.log('主要改进：');
    console.log('- 禁用假流式模式，使用真实流式响应');
    console.log('- 启用标准 <think> 标签格式');
    console.log('- 思考内容现在可以被主流客户端正确识别和折叠');
}

if (require.main === module) {
    optimizePerformance().catch(console.error);
}

module.exports = { optimizePerformance };
`;
    
    fs.writeFileSync(path.join(__dirname, 'optimize-performance-final.js'), optimizerContent, 'utf8');
    console.log('✅ 性能优化脚本已创建');
}

// 创建测试脚本
function createThinkTagTest() {
    const testContent = `#!/usr/bin/env node

/**
 * 测试标准 <think> 标签功能
 */

async function testThinkTags() {
    const apiUrl = 'https://g.551543.xyz';
    const apiKey = 'sk-mariahlamb';
    
    console.log('🧪 测试标准 <think> 标签功能');
    console.log('=' .repeat(50));
    
    const testCases = [
        {
            name: '基础思考测试',
            model: 'gemini-2.5-flash',
            prompt: '请解释什么是人工智能，并思考一下它的发展历程。',
            expectThinkTags: true
        },
        {
            name: '数学推理测试',
            model: 'gemini-2.5-flash',
            prompt: '请计算 123 * 456，并解释计算过程。',
            expectThinkTags: true
        }
    ];
    
    for (const testCase of testCases) {
        console.log(\`\\n🔍 测试: \${testCase.name}\`);
        
        try {
            const response = await fetch(\`\${apiUrl}/v1/chat/completions\`, {
                method: 'POST',
                headers: {
                    'Authorization': \`Bearer \${apiKey}\`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Cherry Studio Test Client'
                },
                body: JSON.stringify({
                    model: testCase.model,
                    messages: [{
                        role: 'user',
                        content: testCase.prompt
                    }],
                    thinking_budget: 1024
                })
            });
            
            const result = await response.json();
            
            if (result.choices && result.choices[0] && result.choices[0].message) {
                const content = result.choices[0].message.content;
                
                // 检查是否包含标准 <think> 标签
                const hasThinkTags = content && content.includes('<think>') && content.includes('</think>');
                const hasMarkdownThinking = content && content.includes('<details>') && content.includes('💭 思考过程');
                const hasSimpleThinking = content && content.includes('**💭 思考：**');
                
                console.log(\`  响应长度: \${content ? content.length : 0} 字符\`);
                console.log(\`  包含 <think> 标签: \${hasThinkTags ? '✅' : '❌'}\`);
                console.log(\`  包含 Markdown 思考: \${hasMarkdownThinking ? '✅' : '❌'}\`);
                console.log(\`  包含简单思考标记: \${hasSimpleThinking ? '✅' : '❌'}\`);
                
                if (hasThinkTags) {
                    // 提取思考内容
                    const thinkMatch = content.match(/<think>([\\s\\S]*?)<\\/think>/);
                    if (thinkMatch) {
                        console.log(\`  思考内容长度: \${thinkMatch[1].length} 字符\`);
                        console.log(\`  思考内容预览: \${thinkMatch[1].substring(0, 100)}...\`);
                    }
                }
                
                if (testCase.expectThinkTags && !hasThinkTags && !hasMarkdownThinking && !hasSimpleThinking) {
                    console.log('  ⚠️ 期望包含思考内容但未检测到');
                }
                
            } else {
                console.log('  ❌ 响应格式异常');
            }
            
        } catch (error) {
            console.log(\`  ❌ 测试失败: \${error.message}\`);
        }
    }
    
    console.log('\\n📋 测试总结:');
    console.log('- 标准 <think> 标签格式应该被 Cherry Studio、SillyTavern 等客户端识别');
    console.log('- Markdown 格式适用于 LobeChat、ChatGPT-Next-Web 等现代客户端');
    console.log('- 简单标记格式提供最佳兼容性');
}

if (require.main === module) {
    testThinkTags().catch(console.error);
}

module.exports = { testThinkTags };
`;
    
    fs.writeFileSync(path.join(__dirname, 'test-think-tags.js'), testContent, 'utf8');
    console.log('✅ <think> 标签测试脚本已创建');
}

// 主函数
function main() {
    console.log('🚀 实现标准 <think> 标签格式的思考内容处理');
    console.log('=' .repeat(60));
    
    console.log('\\n📋 基于研究发现的标准格式:');
    console.log('- DeepSeek R1 使用 <think>...</think> 标签');
    console.log('- KoboldCpp 支持思考标签处理');
    console.log('- 主流客户端 (Cherry Studio, LobeChat, SillyTavern) 支持此格式');
    
    // 1. 更新 worker 文件
    console.log('\\n🔧 更新 worker 文件...');
    if (updateWorkerWithThinkTags()) {
        console.log('✅ Worker 文件更新成功');
    } else {
        console.log('❌ Worker 文件更新失败');
        return;
    }
    
    // 2. 创建性能优化脚本
    console.log('\\n⚡ 创建性能优化脚本...');
    createPerformanceOptimizer();
    
    // 3. 创建测试脚本
    console.log('\\n🧪 创建测试脚本...');
    createThinkTagTest();
    
    console.log('\\n🎉 实现完成！');
    console.log('\\n📝 下一步操作:');
    console.log('1. 运行性能优化: node optimize-performance-final.js');
    console.log('2. 测试 <think> 标签: node test-think-tags.js');
    console.log('3. 在 Cherry Studio 中测试思考功能');
    
    console.log('\\n💡 主要改进:');
    console.log('- 实现了标准 <think>...</think> 标签格式');
    console.log('- 支持客户端自动检测和格式适配');
    console.log('- 兼容 Cherry Studio、LobeChat、SillyTavern 等主流客户端');
    console.log('- 思考内容现在可以被正确识别和折叠显示');
}

if (require.main === module) {
    main();
}

module.exports = {
    updateWorkerWithThinkTags,
    createPerformanceOptimizer,
    createThinkTagTest
};
