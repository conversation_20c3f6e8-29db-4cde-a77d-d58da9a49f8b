# wrangler.toml
# 这是你的 Cloudflare Worker 配置文件。
# 在部署之前，请务必将下面的占位符替换为你的实际信息。

# 你的 Worker 的名称，会显示在 Cloudflare 仪表盘上。
name = "g"

# 你的 Cloudflare 账户 ID。
# 你可以在 Cloudflare 仪表盘主页的右侧找到它。
account_id = "YOUR_ACCOUNT_ID"

# 主入口文件。
main = "worker.js"

# 兼容性日期，确保 Worker 运行时的行为一致。
compatibility_date = "2025-07-23"

# D1 数据库绑定配置。
# 这是将你的 D1 数据库连接到 Worker 的关键部分。
[[d1_databases]]
# 绑定名称，必须与代码中的 `env.db` 匹配。
binding = "db"
# 你的 D1 数据库的名称。
database_name = "gemini-api-keys"
# 你的 D1 数据库的 ID。
database_id = "e10926af-13a8-456e-b974-bf335c1df3f4"

# 环境变量。
# 在这里设置你的管理员凭据和 API 密钥比在仪表盘上手动设置更方便管理。
# 注意：不要将真实的密码直接提交到公共代码库中。
[vars]
ADMIN_USERNAME = "mariahlamb"
ADMIN_PASSWORD = "Aimariah314"
API_KEY = "sk-mariahlamb"
# ACCESS_CONTROL = "restricted" # 可选，根据需要取消注释和设置
# GUEST_PASSWORD = "YOUR_GUEST_PASSWORD" # 可选
