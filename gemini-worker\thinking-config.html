<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Thinking 配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #4facfe;
        }

        .config-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .config-item:last-child {
            margin-bottom: 0;
        }

        .config-label {
            flex: 1;
            margin-right: 20px;
        }

        .config-label h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .config-label p {
            color: #666;
            font-size: 0.9em;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-switch.active {
            background: #4facfe;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active::after {
            transform: translateX(30px);
        }

        .test-section {
            background: #fff3cd;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #ffc107;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: transform 0.2s, box-shadow 0.2s;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }

        .input-group input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .test-results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .test-results pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Gemini Thinking 配置</h1>
            <p>管理 Cloudflare Workers 代理的思考功能设置</p>
        </div>

        <div class="content">
            <!-- 连接配置 -->
            <div class="config-section">
                <h3>🔗 连接配置</h3>
                <div class="input-group">
                    <label for="apiUrl">Worker API URL:</label>
                    <input type="text" id="apiUrl" placeholder="https://your-worker.workers.dev" />
                </div>
                <div class="input-group">
                    <label for="apiKey">API 密钥:</label>
                    <input type="password" id="apiKey" placeholder="your-api-key" />
                </div>
                <button class="btn" onclick="loadConfig()">🔄 加载当前配置</button>
            </div>

            <!-- 思考功能配置 -->
            <div class="config-section">
                <h3>⚙️ 思考功能配置</h3>
                <div class="config-item">
                    <div class="config-label">
                        <h4>显示思考过程</h4>
                        <p>是否在响应中包含 AI 的思考内容</p>
                    </div>
                    <div class="toggle-switch" id="showThinkingToggle" onclick="toggleConfig('showThinking')"></div>
                </div>
                <div class="config-item">
                    <div class="config-label">
                        <h4>思考转内容</h4>
                        <p>将思考内容转换为普通文本内容显示</p>
                    </div>
                    <div class="toggle-switch" id="thoughtToContentToggle" onclick="toggleConfig('thoughtToContent')"></div>
                </div>
                <button class="btn" onclick="saveConfig()">💾 保存配置</button>
            </div>

            <!-- 测试功能 -->
            <div class="test-section">
                <h3>🧪 功能测试</h3>
                <p>测试思考功能是否正常工作</p>
                <button class="btn" onclick="testBasicThinking()">🧠 基础思考测试</button>
                <button class="btn" onclick="testStreamingThinking()">🌊 流式思考测试</button>
                <button class="btn" onclick="testThinkingWithTools()">🔧 思考+工具测试</button>
                <div id="testResults" class="test-results" style="display: none;"></div>
            </div>

            <!-- 状态显示 -->
            <div id="statusMessage"></div>
        </div>
    </div>

    <script>
        let currentConfig = {
            showThinking: false,
            thoughtToContent: false
        };

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        function updateToggle(toggleId, active) {
            const toggle = document.getElementById(toggleId);
            if (active) {
                toggle.classList.add('active');
            } else {
                toggle.classList.remove('active');
            }
        }

        function toggleConfig(configName) {
            currentConfig[configName] = !currentConfig[configName];
            updateToggle(configName + 'Toggle', currentConfig[configName]);
        }

        async function makeRequest(endpoint, method = 'GET', data = null) {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;

            if (!apiUrl || !apiKey) {
                showStatus('请先配置 API URL 和密钥', 'error');
                return null;
            }

            try {
                const options = {
                    method,
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(`${apiUrl}/admin/${endpoint}`, options);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || `HTTP ${response.status}`);
                }

                return result;
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function loadConfig() {
            showStatus('正在加载配置...', 'info');
            
            const result = await makeRequest('thinking-config');
            if (result && result.success) {
                currentConfig = result.data;
                updateToggle('showThinkingToggle', currentConfig.showThinking);
                updateToggle('thoughtToContentToggle', currentConfig.thoughtToContent);
                showStatus('配置加载成功', 'success');
            }
        }

        async function saveConfig() {
            showStatus('正在保存配置...', 'info');
            
            const result = await makeRequest('update-thinking-config', 'POST', currentConfig);
            if (result && result.success) {
                showStatus('配置保存成功', 'success');
            }
        }

        async function testBasicThinking() {
            showStatus('正在进行基础思考测试...', 'info');
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>测试中...</p>';

            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;

            try {
                const response = await fetch(`${apiUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'gemini-2.5-flash',
                        messages: [{
                            role: 'user',
                            content: '请解释量子计算的基本原理，并分析其优势。'
                        }],
                        thinking_budget: 1024
                    })
                });

                const result = await response.json();
                
                let testResult = '✅ 基础思考测试完成\n\n';
                if (result.choices && result.choices[0] && result.choices[0].message) {
                    const content = result.choices[0].message.content;
                    const hasThinking = content && content.includes('💭');
                    testResult += `思考内容检测: ${hasThinking ? '✅ 发现' : '❌ 未发现'}\n`;
                    testResult += `响应长度: ${content ? content.length : 0} 字符\n\n`;
                    testResult += '响应内容预览:\n' + (content ? content.substring(0, 200) + '...' : '无内容');
                } else {
                    testResult += '❌ 响应格式异常';
                }

                resultsDiv.innerHTML = `<pre>${testResult}</pre>`;
                showStatus('基础思考测试完成', 'success');
            } catch (error) {
                resultsDiv.innerHTML = `<pre>❌ 测试失败: ${error.message}</pre>`;
                showStatus('基础思考测试失败', 'error');
            }
        }

        async function testStreamingThinking() {
            showStatus('正在进行流式思考测试...', 'info');
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>流式测试中...</p>';

            // 实现流式测试逻辑
            showStatus('流式思考测试功能开发中...', 'info');
        }

        async function testThinkingWithTools() {
            showStatus('正在进行思考+工具测试...', 'info');
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p>工具测试中...</p>';

            // 实现工具测试逻辑
            showStatus('思考+工具测试功能开发中...', 'info');
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 尝试从 localStorage 恢复配置
            const savedApiUrl = localStorage.getItem('apiUrl');
            const savedApiKey = localStorage.getItem('apiKey');
            
            if (savedApiUrl) document.getElementById('apiUrl').value = savedApiUrl;
            if (savedApiKey) document.getElementById('apiKey').value = savedApiKey;

            // 保存配置到 localStorage
            document.getElementById('apiUrl').addEventListener('change', function() {
                localStorage.setItem('apiUrl', this.value);
            });
            
            document.getElementById('apiKey').addEventListener('change', function() {
                localStorage.setItem('apiKey', this.value);
            });
        });
    </script>
</body>
</html>
