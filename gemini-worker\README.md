# Google Gemini API Key 聚合管理系统



## 部署到 Cloudflare Workers

### 1. 创建 D1 数据库

```bash
# 创建 D1 数据库
wrangler d1 create gemini-api-keys

# 创建必要的表结构
wrangler d1 execute gemini-api-keys --command "
CREATE TABLE IF NOT EXISTS keys (
  key TEXT PRIMARY KEY,
  balance REAL DEFAULT 0,
  added TEXT,
  last_updated TEXT
);

CREATE TABLE IF NOT EXISTS config (
  name TEXT PRIMARY KEY,
  value TEXT
);

-- 插入默认配置
INSERT OR REPLACE INTO config (name, value) VALUES
('admin_username', 'admin'),
('admin_password', 'your-admin-password'),
('api_key', 'your-proxy-api-key'),
('page_size', '12'),
('access_control', 'open'),
('guest_password', 'guest123');
"
```

### 2. 配置 wrangler.toml

```toml
name = "gemini-api-proxy"
main = "worker - D1.js"
compatibility_date = "2024-01-01"

[[d1_databases]]
binding = "db"
database_name = "gemini-api-keys"
database_id = "your-database-id"  # 从步骤1获取

[vars]
# 可选：在这里设置环境变量，或在 Cloudflare Dashboard 中设置
```

### 3. Cloudflare Dashboard 配置

在 Cloudflare Workers Dashboard 中设置以下变量：

#### 环境变量 (推荐设置)
**配置优先级**：环境变量 > D1数据库 > 代码默认值

| 变量名 | 说明 | 示例值 | 优先级 |
|--------|------|--------|--------|
| `ADMIN_USERNAME` | 管理员用户名 | `admin` | **最高** |
| `ADMIN_PASSWORD` | 管理员密码 | `your-secure-password` | **最高** |
| `API_KEY` | 代理API密钥（客户端调用时使用） | `your-proxy-api-key` | **最高** |
| `PAGE_SIZE` | 主界面每页显示的密钥数量 | `12` | 可选 |
| `ACCESS_CONTROL` | 访问控制模式 | `open`/`restricted`/`private` | 可选 |
| `GUEST_PASSWORD` | 访客密码（restricted模式使用） | `guest123` | 可选 |

⚠️ **重要**：环境变量会覆盖 D1 数据库和代码中的所有配置！

### 配置优先级详解

系统按以下优先级读取配置：

1. **🥇 Cloudflare Workers 环境变量**（最高优先级）
   - 在 Cloudflare Dashboard 中设置
   - 立即生效，无需重启
   - 推荐用于敏感信息（密码、API密钥）

2. **🥈 D1 数据库配置**（中等优先级）
   - 通过管理员界面修改
   - 存储在 `config` 表中
   - 适合运行时动态修改的配置

3. **🥉 代码默认值**（最低优先级）
   - 硬编码在 `CONFIG` 常量中
   - 仅在前两者都未设置时使用

**示例**：如果您在环境变量中设置了 `ADMIN_PASSWORD=mypassword`，即使 D1 数据库中有不同的密码，系统也会使用 `mypassword`。

#### D1 数据库绑定
- **变量名**: `db`
- **数据库**: 选择步骤1创建的数据库

### 4. 部署

```bash
# 部署到 Cloudflare Workers
wrangler deploy
```

### 5. 配置自定义域名（可选）

在 Cloudflare Dashboard 中为 Worker 配置自定义域名，例如：
- `https://gemini-api.your-domain.com`

### 6. 初始配置

部署完成后：

1. **访问管理员界面**: `https://your-worker-domain.com/admin`
   - 用户名: `admin`（或您设置的值）
   - 密码: `your-admin-password`（或您设置的值）

2. **获取并添加 Gemini API 密钥**:
   - 访问 [Google AI Studio](https://aistudio.google.com/apikey) 创建 API 密钥
   - 在管理员界面中添加获取的 API 密钥（格式：`AIza...`）
   - 系统会自动验证密钥有效性
   - 支持批量添加多个密钥实现负载均衡

3. **测试 API 调用**:

   **测试模型列表**:
   ```bash
   curl https://your-worker-domain.com/v1/models \
     -H "Authorization: Bearer your-proxy-api-key"
   ```

   **测试聊天对话**:
   ```bash
   curl https://your-worker-domain.com/v1/chat/completions \
     -H "Authorization: Bearer your-proxy-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gemini-2.0-flash",
       "messages": [{"role": "user", "content": "Hello"}]
     }'
   ```

   **使用 OpenAI Python SDK**:
   ```python
   from openai import OpenAI

   client = OpenAI(
       api_key="your-proxy-api-key",
       base_url="https://your-worker-domain.com/v1"
   )

   # 获取真实的 Gemini 模型列表
   models = client.models.list()
   print("可用的 Gemini 模型:")
   for model in models.data:
       print(f"- {model.id}: {model.description}")

   # 使用真实的 Gemini 模型进行聊天
   response = client.chat.completions.create(
       model="gemini-2.0-flash",  # 使用真实的 Gemini 模型名称
       messages=[{"role": "user", "content": "Hello"}]
   )
   print(response.choices[0].message.content)
   ```

## 故障排除

### 常见问题

1. **密钥验证失败**
   - 确保 API 密钥格式正确（以 `AIza` 开头）
   - 检查密钥是否在 Google AI Studio 中有效
   - 确认密钥有足够的配额

2. **无法访问管理员界面**
   - 检查配置优先级：环境变量 > D1数据库 > 默认值
   - 确认用户名和密码设置正确（注意环境变量会覆盖数据库配置）
   - 默认账号：`default-admin-username` / `default-admin-password`
   - 确认 D1 数据库配置正确
   - 查看 Worker 日志排查错误

3. **API 调用失败**
   - 确认代理 API 密钥正确
   - 检查是否有可用的 Gemini API 密钥
   - 查看请求格式是否符合 OpenAI 标准
   - 测试模型列表端点：`GET /v1/models`

4. **模型列举失败**
   - 检查 Gemini API 密钥是否有效
   - 确认网络连接正常
   - 查看 Worker 日志中的错误信息
   - 测试单个密钥：访问 `/debug/config` 查看配置

4. **D1 数据库连接问题**
   - 确认 `wrangler.toml` 中的数据库绑定正确
   - 检查数据库 ID 是否匹配
   - 确认表结构已正确创建

5. **配置不生效问题**
   - 检查环境变量名是否正确（必须大写，如 `ADMIN_USERNAME`）
   - 确认环境变量值不为空
   - 重新部署 Worker 使环境变量生效
   - 使用 `wrangler tail` 查看配置读取日志

### 日志查看

```bash
# 查看 Worker 实时日志
wrangler tail

# 查看特定时间段的日志
wrangler tail --since 1h
```
